<template>
  <div class="tw:my-4">
    <q-card
      class="tw:mt-4 tw:mb-[15rem] tw:bg-white tw:p-4 tw:min-h-[calc(100vh-15rem)] tw:text-[#333333]"
    >
      <div class="tw:text-l-design tw:font-bold tw:mb-4">入荷実績詳細</div>
      <div class="tw:border tw:border-[#E0E0E0] tw:rounded-none">
        <div class="tw:flex tw:flex-col tw:divide-y tw:divide-[#E0E0E0]">
          <!-- code -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              漁獲/荷口番号
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ maskCodeString(detailArrival.code) }}
            </div>
          </div>

          <!-- Supplier -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              仕入先
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ `${detailArrival.starting_user_name || detailArrival.starting_enterprise_name}` }}
            </div>
          </div>

          <!-- license_number -->
          <div
            v-if="
              user.enterprise_type === ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE ||
              user.enterprise_type === ROLES_ENUM_OPTIONS_VALUES.CATCH_STAFF
            "
            class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full"
          >
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              許可番号
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ `${detailArrival.starting_license_number}` }}
            </div>
          </div>

          <!-- note 1 -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              備考１
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ `${detailArrival.starting_user_note_1}` }}
            </div>
          </div>

          <!-- note 2 -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              備考２
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ `${detailArrival.starting_user_note_2}` }}
            </div>
          </div>

          <!-- destination user name -->
          <div
            v-if="
              (user.role === ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE &&
                user.staff_type === STAFF_TYPE_ENUM.STAFF) ||
              (user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
                user.staff_type === STAFF_TYPE_ENUM.ENTERPRISE)
            "
            class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full"
          >
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              入荷者
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              {{ `${detailArrival.destination_user_name}` }}
            </div>
          </div>

          <!-- Destination enterprise name -->
          <div
            v-if="
              detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL ||
              detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY
            "
            class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full"
          >
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              入荷登録単位
            </div>
            <div class="tw:tl:w-[70%] tw:p-4 tw:text-m-design">
              <q-radio
                class="tw:text-m-design"
                size="md"
                v-model="arrivalType"
                val="1"
                label="重量"
              />
              <q-radio
                class="tw:text-m-design tw:pl-3"
                size="md"
                v-model="arrivalType"
                val="2"
                label="尾数"
              />
            </div>
          </div>

          <!-- arrival weight -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              入荷量
              <q-badge
                class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded font-xxs-design tw:justify-center tw:items-center tw:flex tw:tl:ml-1"
              >
                必須
              </q-badge>
            </div>
            <div class="tw:tl:w-[70%] tw:p-4">
              <div
                v-if="
                  (arrivalType === '1' &&
                    detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL) ||
                  (arrivalType === '1' &&
                    detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY) ||
                  detailArrival.shipping_type === SHIPPING_TYPE_ENUM.NORMAL
                "
                class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:justify-between tw:gap-2 tw:items-center tw:my-3"
              >
                <div
                  class="tw:flex-1 tw:flex-col tw:tl:flex-row tw:flex tw:gap-2 tw:tl:items-center"
                >
                  <div
                    class="tw:font-normal tw:text-xs-design tw:mb-1 tw:tl:mb-6 tw:dt:mb-6 tw:min-w-[8rem] tw:dt:min-w-[6rem]"
                  >
                    全体重量
                  </div>
                  <BaseInput
                    class="tw:w-full"
                    clearable
                    outlined
                    type="text"
                    maxlength="12"
                    inputmode="numeric"
                    input-class="tw:text-right tw:text-m-design"
                    autocomplete="nope"
                    lazy-rules
                    :model-value="arrivalGrossWeight"
                    @update:model-value="changeGrossWeight"
                    :mask="{
                      mask: Number, // Chỉ chấp nhận số
                      thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                      scale: 2, // Không cho phép số thập phân
                      signed: false, // Không cho phép số âm
                      min: 0, // Chỉ cho phép số không âm
                      lazy: false, // Hiển thị placeholder ngay lập tức
                      radix: '.',
                      max: 9999999.99,
                      padFractionalZeros: false,
                      normalizeZeros: true,
                    }"
                    :class="{
                      'main-code': settingUser?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY,
                      'tw:mb-[1.2rem] tw:tl:mb-0':
                        !!errors.arrivalGrossWeight || !!errors.arrivalTareWeight,
                    }"
                    :disable="settingUser?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY"
                    :error="!!errors.arrivalGrossWeight"
                    :error-message="errors.arrivalGrossWeight"
                    no-error-icon
                  >
                    <template v-slot:append>
                      <div class="tw:text-m-design tw:text-[#333333]">g</div>
                    </template>
                  </BaseInput>
                </div>
                <div class="tw:flex tw:items-center tw:gap-2">
                  <svg
                    width="41"
                    height="68"
                    viewBox="0 0 41 68"
                    class="tw:text-[#7E8093] tw:tl:mb-5 tw:dt:mb-5"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <rect x="8.5" y="33" width="24" height="2" fill="#7E8093" />
                  </svg>
                  <div
                    class="tw:flex-1 tw:flex-col tw:tl:flex-row tw:flex tw:gap-2 tw:tl:items-center"
                  >
                    <div
                      class="tw:font-normal tw:text-xs-design tw:mb-1 tw:tl:mb-6 tw:dt:mb-6 tw:tl:min-w-[4rem] tw:dt:min-w-[3rem]"
                    >
                      風袋
                    </div>
                    <BaseInput
                      class="tw:w-full"
                      clearable
                      maxlength="12"
                      outlined
                      type="text"
                      inputmode="numeric"
                      autocomplete="nope"
                      lazy-rules
                      :model-value="arrivalTareWeight"
                      @update:model-value="changeTareWeight"
                      :mask="{
                        mask: Number, // Chỉ chấp nhận số
                        thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                        scale: 2, // Không cho phép số thập phân
                        signed: false, // Không cho phép số âm
                        min: 0, // Chỉ cho phép số không âm
                        lazy: false, // Hiển thị placeholder ngay lập tức
                        radix: '.',
                        max: 9999999.99,
                        padFractionalZeros: false,
                        normalizeZeros: true,
                      }"
                      input-class="tw:text-right tw:text-m-design"
                      :class="{
                        'main-code':
                          settingUser?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY,
                        'tw:mb-[1.2rem] tw:tl:mb-0':
                          !!errors.arrivalGrossWeight || !!errors.arrivalTareWeight,
                      }"
                      :disable="settingUser?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY"
                      :error="!!errors.arrivalTareWeight"
                      :error-message="errors.arrivalTareWeight"
                      no-error-icon
                    >
                      <template v-slot:append>
                        <div class="tw:text-m-design tw:text-[#333333]">g</div>
                      </template>
                    </BaseInput>
                  </div>
                </div>
                <div
                  class="tw:flex-1 tw:flex tw:gap-2 tw:items-center tw:justify-between tw:tl:justify-start"
                >
                  <div
                    class="tw:font-normal tw:text-xs-design tw:mb-1 tw:tl:mb-0 tw:min-w-[5rem] tw:dt:min-w-[6rem]"
                  >
                    入荷量
                  </div>
                  <div
                    v-if="arrivalType === '1'"
                    class="tw:text-m-design tw:font-bold tw:tl:font-normal"
                  >
                    {{ arrivalNetWeight }}g
                  </div>
                </div>
              </div>
              <div
                class="tw:py-7"
                v-if="
                  (arrivalType === '2' &&
                    detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL) ||
                  (arrivalType === '2' && detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY)
                "
              >
                <BaseInput
                  class="tw:w-full tw:tl:w-[35%]"
                  clearable
                  outlined
                  type="text"
                  maxlength="12"
                  inputmode="numeric"
                  input-class="tw:text-right tw:text-m-design"
                  autocomplete="nope"
                  lazy-rules
                  :model-value="arrivalQuantity"
                  @update:model-value="changeArrivalQuantity"
                  :mask="{
                    mask: Number, // Chỉ chấp nhận số
                    thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                    scale: 2, // Không cho phép số thập phân
                    signed: false, // Không cho phép số âm
                    min: 0, // Chỉ cho phép số không âm
                    lazy: false, // Hiển thị placeholder ngay lập tức
                    radix: '.',
                    max: 9999999.99,
                    padFractionalZeros: false,
                    normalizeZeros: true,
                  }"
                  :class="{
                    'main-code': settingUser?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY,
                    'tw:mb-[1.2rem] tw:tl:mb-0':
                      !!errors.arrivalQuantity || !!errors.arrivalTareWeight,
                  }"
                  :disable="settingUser?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY"
                  :error="!!errors.arrivalQuantity"
                  :error-message="errors.arrivalQuantity"
                  no-error-icon
                >
                  <template v-slot:append>
                    <div class="tw:text-m-design tw:text-[#333333]">尾</div>
                  </template>
                </BaseInput>
              </div>
            </div>
          </div>

          <!-- arrival date -->
          <div class="tw:flex tw:tl:flex-row tw:flex-col tw:w-full">
            <div
              class="tw:tl:w-[30%] tw:bg-[#D2D2D2] tw:pl-5 tw:pr-5 tw:p-4 tw:font-[400] tw:text-m-design tw:items-center tw:flex tw:justify-between"
            >
              入荷日
              <q-badge
                class="tw:text-white tw:bg-[#E80F00] tw:p-1 tw:rounded font-xxs-design tw:justify-center tw:items-center tw:flex tw:tl:ml-1"
              >
                必須
              </q-badge>
            </div>
            <div class="tw:tl:w-[25%] tw:p-4">
              <BaseDatePicker
                v-model="arrivalDate"
                :error="!!errors.arrivalDate"
                :error-message="errors.arrivalDate"
                input-class="tw:text-m-design"
              />
            </div>
          </div>
        </div>
      </div>
    </q-card>
    <!-- Buttons -->
    <q-footer
      elevated
      class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full tw:items-center
      tw:flex tw:justify-center tw:tl:justify-between tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="修正をやめる"
        @click.prevent="goToPage('arrivalDetail')"
      />

      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="修正する"
        @click.prevent="editArrival"
      />
    </q-footer>
    <PopupConfirmArrival />
    <ReasonDifference />
  </div>
</template>
<script setup>
// #region import
import { ref, onMounted, provide, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import arrivalService from 'services/arrival.service';
import {
  maskCodeString,
  FORMAT_NUMBER,
  isNumeric,
  doParseFloatNumber,
  FORMAT_DATE,
} from 'helpers/common';
import { makeQuantityXML, makeXML } from 'boot/print';
import dayjs from 'boot/dayjs';
import {
  UNIT_TYPE_SETTING_ENUM,
  SHIPPING_TYPE_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
} from 'helpers/constants';
import useValidate from 'composables/validate';
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import PopupConfirmArrival from 'components/PopupConfirmArrival.vue';
import editArrivalSchema from 'schemas/editArrival';
import MESSAGE from 'helpers/message';
import toast from 'utilities/toast';
import {
  ENTERPRISE_TYPE_ENUM,
  ROLES_ENUM_OPTIONS_VALUES,
  STAFF_TYPE_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
} from 'src/helpers/constants';
import { useAuthStore } from 'src/stores/auth-store';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import systemSettingsAdminService from 'src/shared/services/admin/systemSettings.admin.service';

import ReasonDifference from './components/ReasonDifference.vue';

// #endregion import

// #region variable
const { settingUser } = storeToRefs(useAppStore());
const router = useRouter();
const { errors, validateData } = useValidate();
const { user } = storeToRefs(useAuthStore());
const isStaff = ref(false);
const isHasLicense = ref(false);
const detailArrival = ref({});
const arrivalId = ref();
const arrivalType = ref('1');
const arrivalDate = ref('');
const arrivalGrossWeight = ref();
const arrivalTareWeight = ref();
const arrivalNetWeight = ref();
const arrivalQuantity = ref();
const unitPerGram = ref(settingUser.value?.unit_per_gram ?? 0);
const shippingNetWeight = ref();
const isPopupReason = ref(false);
const typeDiff = ref(TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH);
const reasonDiff = ref('');
const reasonInfo = ref({});
const systemSetting = ref(null);
const originalArrivalData = ref({});
const hasChangedArrivalData = ref(false);

// #endregion variable

// #region function
const changeGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const arrivalGrossWeightNum = doParseFloatNumber(newValue || 0);
  const arrivalTareWeightNum = doParseFloatNumber(arrivalTareWeight.value || 0);
  const arrivalNetWeightNum = arrivalGrossWeightNum - arrivalTareWeightNum;
  arrivalGrossWeight.value = newValue ? FORMAT_NUMBER(arrivalGrossWeightNum) : newValue;
  arrivalNetWeight.value =
    arrivalNetWeightNum >= 0 ? FORMAT_NUMBER(arrivalNetWeightNum) : undefined;
  arrivalQuantity.value =
    arrivalNetWeightNum >= 0
      ? FORMAT_NUMBER(Math.ceil((arrivalNetWeightNum / unitPerGram.value).toFixed(3)))
      : undefined;
  checkArrivalDataChanges();
};

const changeTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const arrivalGrossWeightNum = doParseFloatNumber(arrivalGrossWeight.value || 0);
  const arrivalTareWeightNum = doParseFloatNumber(newValue || 0);
  const arrivalNetWeightNum = arrivalGrossWeightNum - arrivalTareWeightNum;
  arrivalTareWeight.value = newValue ? FORMAT_NUMBER(arrivalTareWeightNum) : newValue;
  arrivalNetWeight.value =
    arrivalNetWeightNum >= 0 ? FORMAT_NUMBER(arrivalNetWeightNum) : undefined;
  arrivalQuantity.value =
    arrivalNetWeightNum >= 0
      ? FORMAT_NUMBER(Math.ceil((arrivalNetWeightNum / unitPerGram.value).toFixed(3)))
      : undefined;
  checkArrivalDataChanges();
};

const changeArrivalQuantity = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const arrivalQuantityNum = doParseFloatNumber(newValue || 0);
  arrivalTareWeight.value = undefined;
  arrivalQuantity.value = newValue ? FORMAT_NUMBER(arrivalQuantityNum, 0) : newValue;
  const arrivalNetWeightNum =
    Math.ceil(doParseFloatNumber(arrivalQuantity.value) * (unitPerGram.value * 100)) / 100;
  arrivalNetWeight.value = doParseFloatNumber(arrivalQuantity.value)
    ? FORMAT_NUMBER(arrivalNetWeightNum)
    : undefined;
  arrivalGrossWeight.value =
    doParseFloatNumber(arrivalQuantity.value) || doParseFloatNumber(arrivalQuantity.value) === 0
      ? FORMAT_NUMBER(arrivalNetWeightNum)
      : undefined;
  if (newValue === '') {
    arrivalGrossWeight.value = '';
  }
  checkArrivalDataChanges();
};

const confirmFunc = async () => {
  const data = {
    arrivalDate: arrivalDate.value,
    arrivalGrossWeight: doParseFloatNumber(arrivalGrossWeight.value),
    arrivalTareWeight: doParseFloatNumber(arrivalTareWeight.value) || undefined,
    arrivalQuantity: doParseFloatNumber(arrivalQuantity.value),
    typeDiff: typeDiff.value || undefined,
    reasonDiff: reasonDiff.value || undefined,
  };
  const result = await arrivalService.editArrival(arrivalId.value, data);
  if (result.code === 0) {
    if (hasChangedArrivalData.value && detailArrival.value.shipping_type === SHIPPING_TYPE_ENUM.PROXY ) {
      await printReceipt();
    }
    toast.access(MESSAGE.MSG_FIXED_ARRIVAL_INFO);
    await router.push({
      name: 'arrivalList',
    });
  }
};

const popupConfirmArrival = ref({
  isPopup: false,
  titlePopup: '修正確認',
  captionPopup: '以下の内容で、入荷実績を修正します',
  listItems: [],
  confirmFunc,
  minWidthDefault: 60,
  minWidthTlDefault: 80,
  minWidthDtDefault: 123,
});

const editArrival = async () => {
  reasonInfo.value = {
    shipping_net_weight: shippingNetWeight.value || 0,
    arrivalNetWeight: arrivalNetWeight.value,
  };
  if (settingUser.value?.display_shipment_weight) {
    reasonInfo.value.displayShipment = settingUser.value?.display_shipment_weight;
  }
  const shippingWeight = +doParseFloatNumber(shippingNetWeight.value) || 0;
  if (
    (Math.abs(shippingWeight - +doParseFloatNumber(arrivalNetWeight.value)) / shippingWeight) *
      100 >
    systemSetting.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]
  ) {
    typeDiff.value = '';
    reasonDiff.value = '';
    isPopupReason.value = true;
    return;
  }

  // Data complate
  errors.value = {};
  const data = {
    arrivalDate: arrivalDate.value,
    arrivalGrossWeight:
      arrivalGrossWeight.value === '' ? '' : doParseFloatNumber(arrivalGrossWeight.value) || 0,
    arrivalTareWeight: arrivalTareWeight.value
      ? doParseFloatNumber(arrivalTareWeight.value)
      : undefined,
    arrivalQuantity:
      arrivalType.value === '2' ? doParseFloatNumber(arrivalQuantity.value) ?? '' : undefined,
  };
  const valid = validateData(editArrivalSchema, data);
  if (!valid) {
    return;
  }
  popupConfirmArrival.value.listItems = [
    { key: '入荷登録単位', value: convertArrivalRegistration() },
    { key: '入荷量', badge: true, value: convertAmountStock(), vHtml: true },
    { key: '入荷日', value: `${arrivalDate.value}`, badge: true },
  ];
  if (arrivalType.value === '2') {
    popupConfirmArrival.value.listItems[1] = {
      key: '入荷量',
      badge: true,
      value: convertAmountStockNumber(),
      vHtml: true,
    };
  }
  popupConfirmArrival.value.isPopup = true;
};

const goToPage = name => {
  router.push({ name });
};

const convertArrivalRegistration = () => (arrivalType.value === '1' ? '重量' : '尾数');

function convertAmountStock() {
  const amountStock = `
  <div class="">
  <div class="tw:flex tw:items-center tw:gap-2">
  <div class="tw:text-m-design">${arrivalGrossWeight.value} g</div>
  <svg width="25" height="48" viewBox="0 0 41 65" fill="none" class="tw:text-[#7E8093]"
    xmlns="http://www.w3.org/2000/svg">
    <rect x="8.5" y="33" width="24" height="2" fill="#7E8093"/>
  </svg>
  <div class="tw:text-m-design tw:text-[#333333]">${arrivalTareWeight.value} g</div>
  </div>
  <div class="tw:text-m-design tw:text-[#333333]"><span class="tw:text-xs-design">入荷量</span> ${arrivalNetWeight.value} g</div>
  </div>
  `;
  return amountStock;
}

function convertAmountStockNumber() {
  const amountStockNum = `
    <div class="tw:text-m-design tw:pt-2">${arrivalQuantity.value} 尾</div>
  `;
  return amountStockNum;
}

const confirmReason = () => {
  popupConfirmArrival.value.listItems = [
    { key: '入荷登録単位', value: convertArrivalRegistration() },
    { key: '入荷量', badge: true, value: convertAmountStock(), vHtml: true },
    { key: '入荷日', value: `${arrivalDate.value}`, badge: true },
  ];
  if (arrivalType.value === '2') {
    popupConfirmArrival.value.listItems[1] = {
      key: '入荷量',
      badge: true,
      value: convertAmountStockNumber(),
      vHtml: true,
    };
  }
  if (reasonDiff.value) {
    popupConfirmArrival.value.listItems.splice(0, 1);
    popupConfirmArrival.value.listItems.push({
      key: '差異の理由',
      value: `${reasonDiff.value}`,
      badge: true,
    });
  }
  popupConfirmArrival.value.isPopup = true;
};

const printReceipt = async () => {
  const receiptNumber = settingUser.value?.receipt_number || 1;
  if (settingUser.value?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY) {
    // In theo số lượng (尾数)
    const dataPrint = makeQuantityXML(
      detailArrival.value.destination_enterprise_name,
      detailArrival.value.destination_user_name,
      maskCodeString(detailArrival.value.code?.replaceAll('-', '')),
      FORMAT_NUMBER(arrivalQuantity.value),
      dayjs(FORMAT_DATE(arrivalDate.value)).toDate(),
      settingUser.value?.price_per_quantity?.[0] || '',
      FORMAT_NUMBER((doParseFloatNumber(arrivalQuantity.value) * (detailArrival.value?.setting?.price_per_quantity?.[0] || 0)) || ''),
      detailArrival.value.starting_enterprise_name || detailArrival.value.starting_user_name,
      detailArrival.value.starting_user_name,
      detailArrival.value.starting_license_number || '',
      detailArrival.value.starting_user_note_1 || '',
      receiptNumber
    );
    const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;
    const aTag = document.createElement('a');
    aTag.href = href;
    aTag.click();
  } else {
    const pricePerKilogram = detailArrival.value?.setting?.price_per_kilogram || 0;
    const price = Number(doParseFloatNumber(arrivalNetWeight.value)) * Number(pricePerKilogram);

    const dataPrint = makeXML(
      detailArrival.value.destination_enterprise_name,
      detailArrival.value.destination_user_name,
      maskCodeString(detailArrival.value.code?.replaceAll('-', '')),
      arrivalGrossWeight.value,
      arrivalTareWeight.value,
      arrivalNetWeight.value,
      dayjs(FORMAT_DATE(arrivalDate.value)).toDate(),
      pricePerKilogram || '',
      FORMAT_NUMBER(price || ''),
      detailArrival.value.starting_enterprise_name || detailArrival.value.starting_user_name,
      detailArrival.value.starting_user_name,
      detailArrival.value.starting_license_number || '',
      detailArrival.value.starting_user_note_1 || '',
      receiptNumber
    );
    const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;
    const aTag = document.createElement('a');
    aTag.href = href;
    aTag.click();
  }
};

const checkArrivalDataChanges = () => {
  const currentNetWeight = arrivalType.value === '1' ?
    doParseFloatNumber(arrivalNetWeight.value) :
    doParseFloatNumber(arrivalQuantity.value);
  const originalNetWeight = arrivalType.value === '1' ?
    doParseFloatNumber(originalArrivalData.value.arrival_net_weight) :
    doParseFloatNumber(originalArrivalData.value.arrival_quantity);

  const currentDate = arrivalDate.value;
  const originalDate = FORMAT_DATE(originalArrivalData.value.arrival_date);

  hasChangedArrivalData.value =
    currentNetWeight !== originalNetWeight ||
    currentDate !== originalDate;
};

// #endregion function

// provide
provide('popupConfirmArrival', popupConfirmArrival);
provide('isPopupReason', isPopupReason);
provide('typeDiff', typeDiff);
provide('reasonDiff', reasonDiff);
provide('confirmReason', confirmReason);
provide('reasonInfo', reasonInfo);

watch(arrivalDate, () => {
  checkArrivalDataChanges();
});

onMounted(async () => {
  arrivalId.value = router.currentRoute.value.params?.id;
  arrivalType.value =
    settingUser.value?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY ? '2' : '1';
  const result = await arrivalService.getArrivalDetail(arrivalId.value);
  if (result.code === 0) {
    detailArrival.value = result.payload;
    isHasLicense.value =
      result.payload?.destination_user?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE;

    isStaff.value =
      result.payload.destination_user?.enterprise_type ===
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
      result.payload.destination_user?.staff_type === STAFF_TYPE_ENUM.STAFF &&
      user.value?.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
      user.value?.staff_type === STAFF_TYPE_ENUM.ENTERPRISE;

    arrivalDate.value = FORMAT_DATE(result.payload?.arrival_date);
    arrivalGrossWeight.value = FORMAT_NUMBER(result.payload?.arrival_gross_weight);
    arrivalTareWeight.value = FORMAT_NUMBER(result.payload?.arrival_tare_weight);
    arrivalNetWeight.value = FORMAT_NUMBER(result.payload?.arrival_net_weight);
    arrivalQuantity.value = FORMAT_NUMBER(result.payload?.arrival_quantity);
    shippingNetWeight.value = FORMAT_NUMBER(result.payload?.shipping_net_weight);

    originalArrivalData.value = {
      arrival_date: result.payload?.arrival_date,
      arrival_net_weight: result.payload?.arrival_net_weight,
      arrival_quantity: result.payload?.arrival_quantity,
    };

    const systemSettingResponse = await systemSettingsAdminService.getSystemSettingsForNormalUser();
    if (systemSettingResponse) {
      systemSetting.value = systemSettingResponse.payload;
    }
  }
});
</script>

<style scoped></style>
