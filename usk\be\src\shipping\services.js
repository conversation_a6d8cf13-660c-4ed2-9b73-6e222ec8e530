const BaseService = require('../base/serviceFn');
const { ControlledException } = require('../base/errors');
const { MESSAGE } = require('../utils/message');
const { Prisma } = require('@prisma/client');
const dayjs = require('../boot/dayjs');
const {
  LIMIT_EXPORT,
  ROLES_ENUM,
  SHIPPING_TYPE_ENUM,
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  TOTAL_LIMIT_EXPORT,
} = require('../helpers/enum');

class ShippingService extends BaseService {
  // ====== 1. Private method ==========
  async #checkCanEditShipping(shippingId, user) {
    const connect = this.DB.READ;
    const data = await connect.the_origins.findFirst({
      where: {
        id: shippingId,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          // only starting user can edit
          id: user.id,
        },
      },
      select: {
        arrival_date: true,
        shipping_type: true,
      },
    });
    if (
      !data ||
      data.shipping_type === SHIPPING_TYPE_ENUM.DISTRIBUTION ||
      data.arrival_date
    ) {
      return false;
    }
    return true;
  }

  async #checkCanDelShipping(shippingId, user) {
    const connect = this.DB.READ;
    const data = await connect.the_origins.findFirst({
      where: {
        id: shippingId,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          // only starting user can delete
          id: user.id,
        },
      },
      select: {
        arrival_date: true,
        shipping_type: true,
        ingredient: true,
        destination_enterprise: {
          select: {
            type: true,
          }
        }
      },
    });

    if (!data) {
      return false;
    }

    // if the destination enterprise is foreign, then can delete
    if (data.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN) {
      return true;
    }

    // if the shipment is already arrived, then cannot delete
    if (data.arrival_date) {
      return false;
    }
    return true;
  }

  // ====== 2. Public method ==========
  async getShippingList(user, query) {
    const {
      page,
      limit,
      destination,
      startDate,
      endDate,
      licenseNumber,
      note1,
      note2,
      code,
      enterpriseName,
    } = query;
    const connect = this.DB.READ;

    const searchCondition = {
      delete_flag: false,
      starting_enterprise_id: user.enterprise_id,
      shipping_net_weight: {
        not: null,
      },
      starting_user_id: user.staff_type === STAFF_TYPE_ENUM.STAFF ? user.id : undefined,
      destination_license_number: licenseNumber
        ? {
          contains: licenseNumber.replace(/\s+/g, ''),
        }
        : undefined,
      destination_user_note_1: note1
        ? {
          contains: note1.replace(/\s+/g, ''),
        }
        : undefined,
      destination_user_note_2: note2
        ? {
          contains: note2.replace(/\s+/g, ''),
        }
        : undefined,
      destination_user_name: destination
        ? {
          contains: destination.replace(/\s+/g, '')
        } : undefined,
      destination_enterprise_name: enterpriseName
        ? {
          contains: enterpriseName.replace(/\s+/g, '')
        } : undefined,
      destination_enterprise: code ? {
        OR: [
          {
            enterprise_code: {
              contains: code.replace(/\s+/g, '')
            }
          }
        ]
      } : undefined,
      shipping_date: {
        gte: startDate
          ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate()
          : undefined,
        lte: endDate
          ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate()
          : undefined,
      },
    };

    const totalCount = await connect.the_origins.count({
      where: searchCondition,
    })

    if (totalCount > TOTAL_LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_TOO_MANY_RESULTS);
    }

    if (totalCount === 0) {
      return this.SUCCESS({
        items: [],
        total_item: 0,
        page: 1,
        page_size: +limit,
      });
    }

    const offset = (+page - 1) * +limit;
    let tempPage = +page;

    // if offset is greater than total item, recalculate page and offset
    if (offset >= totalCount) {
      tempPage = Math.ceil(totalCount / +limit);
    }


    const data = await connect.the_origins.findMany({
      where: searchCondition,
      select: {
        id: true,
        shipping_date: true,
        code: true,
        shipping_net_weight: true,
        starting_user_name: true,
        starting_license_number: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        destination_license_number: true,
        destination_enterprise_name: true,
        shipping_type: true,
        destination_enterprise: {
          select: {
            enterprise_name: true,
            enterprise_code: true,
          },
        },
        destination_user: {
          select: {
            user_code: true,
            name: true,
            role: true,
            staff_type: true,
            enterprise_type: true,
          },
        },
        starting_enterprise: {
          select: {
            enterprise_name: true,
            enterprise_code: true,
          },
        },
        starting_user: {
          select: {
            license_number: true,
            role: true,
            name: true,
            staff_type: true,
            enterprise_type: true,
          },
        },
      },
      orderBy: { shipping_date: 'desc' }
    });
    return this.SUCCESS({
      items: data,
      total_item: totalCount,
      page: tempPage,
      page_size: +limit,
    });
  }

  async getShippingDetail(params) {
    const { user, id } = params;
    const connect = this.DB.READ;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 400);
    }
    const data = await connect.the_origins.findFirst({
      where: {
        delete_flag: false,
        id: +id,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          id: user.staff_type === STAFF_TYPE_ENUM.STAFF
            ? user.id
            : undefined,
        },
      },
      select: {
        qr_code: true,
        code: true,
        shipping_net_weight: true,
        shipping_gross_weight: true,
        shipping_tare_weight: true,
        shipping_quantity: true,
        shipping_date: true,
        arrival_date: true,
        shipping_type: true,
        starting_user_name: true,
        starting_license_number: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        destination_license_number: true,
        destination_enterprise_name: true,
        destination_user_note_1: true,
        destination_user_note_2: true,
        starting_user_note_1: true,
        starting_user_note_2: true,
        shipping_reason_diff: true,
        shipping_id_list: true,
        starting_user: {
          select: {
            role: true,
            user_code: true,
            enterprise_type: true,
            staff_type: true,
          },
        },
        destination_enterprise: {
          select: {
            type: true,
          },
        },
        destination_user_id: true,
        destination_user: {
          select: {
            enterprise_type: true,
            staff_type: true,
          },
        },
        inventory: {
          select: {
            is_history_cancel_locked: true,
          },
        },
        ingredient: true,
      },
    });
    if (!data) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 402);
    }
    const canEdit = await this.#checkCanEditShipping(+id, user);
    const canDel = await this.#checkCanDelShipping(+id, user);
    const foreignFlag = data.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN

    // get inventory information - {the_origin_codes, supplier_name}
    let inventory_info_codes = [];
    console.log(123, data.shipping_id_list);

    if (data.shipping_id_list[0] && data.shipping_id_list.length) {
      console.log(data.shipping_id_list[0], data.shipping_id_list.length);

      const lstId = data.shipping_id_list.map(item => item.id);
      const inventoryList = await connect.the_origins.findMany({
        where: {
          id: {
            in: lstId,
          },
          delete_flag: false,
          arrival_date: {
            not: null,
          },
        },
        select: {
          id: true,
          code: true,
          destination_enterprise_name: true,
          destination_user_name: true,
        },
      });
      console.log(234, inventoryList, lstId);


      // calculate the code and supplier name
      inventoryList.forEach(inventory => {
        inventory_info_codes.push({
          code: inventory.code,
          supplier_name: inventory.destination_enterprise_name,
        });
      });

      delete data.ingredient;
    }

    return this.SUCCESS({
      ...data,
      can_edit: canEdit,
      can_del: !!canDel,
      foreign_flag: foreignFlag,
      inventory_info_codes: inventory_info_codes.length ? inventory_info_codes : undefined,
    });
  }

  async cancelShipping(params) {
    const { id, user } = params;
    const connectWrite = this.DB.WRITE;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 400);
    }
    const canDel = await this.#checkCanDelShipping(+id, user);
    if (!canDel) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 402);
    }

    const theOrigin = await connectWrite.the_origins.findFirst({
      where: {
        id: +id,
      },
      select: {
        ingredient: true,
      },
    });

    await connectWrite.$transaction(
      async (tx) => {
        await tx.the_origins.update({
          where: {
            id: +id,
          },
          data: {
            delete_flag: true,
            latest_updated_by_id: user.id,
            latest_updated_on: dayjs().toDate(),
          },
        });
        await Promise.all(
          theOrigin.ingredient.map((item) => {
            if (item.shipping_inventory_id) {
              return tx.inventories.update({
                where: {
                  id: item.shipping_inventory_id,
                },
                data: {
                  net_weight_inventory: {
                    increment:
                      item.taken_inventory_weight,
                  },
                },
              });
            }
          })
        );
      },
      { isolationLevel: Prisma.TransactionIsolationLevel.Serializable }
    );

    return this.SUCCESS({ message: MESSAGE.MSG_CANCEL_SHIPPING_INFO });
  }

  async editShipping(params) {
    const { id, user, ...body } = params;
    const connectRead = this.DB.READ;
    const connectWrite = this.DB.WRITE;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 400);
    }
    const {
      shippingGrossWeight,
      shippingTareWeight,
      shippingQuantity,
      shippingDate,
      destinationId,
      reasonDiff,
      typeDiff,
    } = body;

    const canEdit = await this.#checkCanEditShipping(+id, user);
    if (!canEdit) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 402);
    }
    const checkOrigin = await connectRead.the_origins.findFirst({
      where: {
        id: +id,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user: {
          id: user.staff_type === STAFF_TYPE_ENUM.STAFF
            ? user.id
            : undefined,
        },
      },
    });
    const setting = await connectRead.settings.findFirst({
      where: {
        user_id: user.id,
      },
      select: {
        display_shipment_weight: true,
        unit_per_gram: true,
      },
    });
    const destination = await connectRead.users.findFirst({
      where: {
        id: destinationId,
        delete_flag: false,
      },
      select: {
        id: true,
        enterprise_id: true,
      },
    });
    if (!checkOrigin || !setting || !destination) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 402);
    }
    const codeFirst = checkOrigin.code.slice(0, 7);
    const codeEnd = checkOrigin.code.slice(13);

    await connectWrite.the_origins.update({
      where: {
        id: +id,
      },
      data: {
        shipping_gross_weight: shippingGrossWeight,
        shipping_tare_weight: shippingTareWeight,
        shipping_net_weight: shippingGrossWeight - shippingTareWeight,
        shipping_quantity: shippingQuantity,
        shipping_date: dayjs(shippingDate).toDate(),
        destination_enterprise_id: destination.enterprise_id,
        destination_user_id: destinationId,
        latest_updated_by_id: user.id,
        latest_updated_on: dayjs().toDate(),
        setting: {
          display_shipment_weight: setting.display_shipment_weight,
          unit_per_gram: setting.unit_per_gram,
        },
        code: `${codeFirst}${dayjs(shippingDate).format('YYMMDD')}${codeEnd}`,
        shipping_type_diff: typeDiff,
        shipping_reason_diff: reasonDiff,
      },
    });
    return this.SUCCESS({ message: MESSAGE.MSG_FIXED_SHIPPING_INFO });
  }

  async exportData(params) {
    const { user, query } = params;
    const {
      startDate,
      endDate,
      licenseNumber,
      destination,
      sortBy,
      note1,
      note2,
      id,
      enterpriseName,
      code,
    } = query;
    const connect = this.DB.READ;
    const searchCondition = {
      id: id ? +id : undefined,
      delete_flag: false,
      starting_enterprise_id: user.enterprise_id,
      shipping_net_weight: {
        not: null,
      },
      starting_user_id: user.staff_type === STAFF_TYPE_ENUM.STAFF ? user.id : undefined,
      destination_license_number: licenseNumber ? { contains: licenseNumber } : undefined,
      destination_user_note_1: note1 ? { contains: note1 } : undefined,
      destination_user_note_2: note2 ? { contains: note2 } : undefined,
      destination_user_name: destination ? { contains: destination } : undefined,
      destination_enterprise_name: enterpriseName ? { contains: enterpriseName } : undefined,
      destination_enterprise: code ? {
        OR: [
          {
            enterprise_code: {
              contains: code
            }
          }
        ]
      } : undefined,
      shipping_date: {
        gte: startDate
          ? dayjs.getDateFromJST(`${startDate} 00:00:00`).toDate()
          : undefined,
        lte: endDate
          ? dayjs.getDateFromJST(`${endDate} 23:59:59`).toDate()
          : undefined,
      },
    };

    const filter = {
      select: {
        shipping_date: true,
        code: true,
        shipping_net_weight: true,
        starting_user_name: true,
        starting_license_number: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        destination_license_number: true,
        destination_enterprise_name: true,
        reason_diff: true,
        destination_enterprise: {
          select: {
            enterprise_name: true,
          },
        },
        destination_user: {
          select: {
            user_code: true,
            name: true,
            role: true,
          },
        },
        starting_user: {
          select: {
            user_code: true,
            note_1: true,
            note_2: true,
            license_number: true,
            role: true,
            name: true,
          },
        },
        starting_enterprise: {
          select: {
            enterprise_name: true,
          },
        },
        shipping_id_list: true,
      },
      where: searchCondition
    };

    const count = await connect.the_origins.count({
      where: searchCondition,
    });

    if (count > LIMIT_EXPORT) {
      throw new ControlledException(MESSAGE.MSG_LIMIT_EXPORT);
    }

    const data = await connect.the_origins.findMany({
      ...filter,
      orderBy:
        sortBy === 'id'
          ? { id: 'desc' }
          : sortBy === 'code'
            ? {
              destination_enterprise: {
                enterprise_name_kana_nospace: 'asc',
              },
            }
            : sortBy === 'shipping_net_weight'
              ? { shipping_net_weight: 'desc' }
              : {},
    });

    return this.SUCCESS({
      data,
    });
  }

  async #getShippingInfo(connect, id) {
    const data = await connect.the_origins.findFirst({
      where: {
        id: +id,
        delete_flag: false,
      },
      select: {
        id: true,
        shipping_id_list: true,
        code: true,
        qr_code: true,
        shipping_net_weight: true,
        shipping_date: true,
        arrival_date: true,
        destination_enterprise_name: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        starting_user_name: true,
        arrival_net_weight: true,
        starting_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },
        destination_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },
      },
    });
    if (!data || !data.shipping_id_list.length) {
      return data;
    }
    const lstId = [];
    data.shipping_id_list.forEach(value => {
      if (value.the_origins) {
        value.the_origins.forEach(o => lstId.push(o.id))
      }
    })
    data.shippingInfo = await Promise.all(lstId.map(value => this.#getShippingInfo(connect, value)));
    return data;
  }

  async exportShippingDetail(params) {
    const { user, query } = params;
    const { id } = query;
    const connect = this.DB.READ;
    let result;

    // Validate id parameter
    if (!id || isNaN(+id) || +id <= 0) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 400);
    }

    const data = await connect.the_origins.findFirst({
      where: {
        id: +id,
        delete_flag: false,
        starting_enterprise_id: user.enterprise_id,
        starting_user_id: user.role === ROLES_ENUM.NORMAL_USER && user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE
          && user.staff_type === STAFF_TYPE_ENUM.STAFF
          ? user.id
          : undefined,
      },
      select: {
        id: true,
        destination_enterprise: {
          select: {
            type: true,
          },
        },
        shipping_id_list: true,
        code: true,
        qr_code: true,
        shipping_net_weight: true,
        shipping_date: true,
        arrival_date: true,
        destination_enterprise_name: true,
        starting_enterprise_name: true,
        destination_user_name: true,
        starting_user_name: true,
        starting_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },
        destination_user: {
          select: {
            enterprise_type: true,
            role: true,
            staff_type: true,
          },
        },

      },
    });

    if (!data) {
      result = null;
    }

    if (+data.destination_enterprise.type !== ENTERPRISE_TYPE_ENUM.FOREIGN) {
      result = data;
    }

    result = await this.#getShippingInfo(connect, +id);

    if (!result) {
      throw new ControlledException(MESSAGE.MSG_NO_DATA_INFO, {}, 402);
    }

    result.foreign_flag = +data.destination_enterprise.type === ENTERPRISE_TYPE_ENUM.FOREIGN;
    return this.SUCCESS(result);
  }

  async getExportToday(params) {
    const { user } = params;
    const connect = this.DB.READ;
    const statisticsDateFrom = await connect.users.findFirst({
      where: {
        id: user.id,
      },
      select: {
        statistics_date_from: true,
        created_on: true,
        enterprise_type: true,
        staff_type: true,
      },
    });
    const dateCompare = statisticsDateFrom.statistics_date_from
      ? dayjs(statisticsDateFrom.statistics_date_from)
      : dayjs(statisticsDateFrom.created_on);
    const checkDiff = dayjs().diff(dateCompare.format('YYYY-MM-DD'), 'day');
    const filter = {
      select: {
        destination_enterprise: true,
        user_create: true,
        starting_user_id: true,
      },
      where: {
        NOT: {
          shipping_net_weight: null,
        },
        AND: {
          delete_flag: false,
          destination_enterprise_id: user.enterprise_id,
          shipping_type: 1,
          shipping_date: {
            lte: dayjs.getDate().toDate(),
            gte:
              checkDiff === 0
                ? dateCompare
                : dayjs.getDate().hour(0).minute(0).second(0).toDate(),
          },
          arrival_net_weight: {
            gt: 0
          }
        },
      },
    };
    const isNotStaff = user.user_code?.endsWith('-0000');
    if (!isNotStaff) {
      filter.where.AND.destination_user_id = user.id;
    }
    const data = await connect.the_origins.findMany({
      ...filter,
    });
    const result = {
      persons: [],
      weight: 0,
    };
    data.reduce((init, item) => {
      init.persons.push(item.starting_user_id);
      init.weight += +item.shipping_net_weight;
      return init;
    }, result);
    return this.SUCCESS({
      weight: result.weight,
      persons: new Set([...result.persons]).size,
      date: dateCompare.format('YYYY/MM/DD'),
    });
  }

  async resetExportToday(params) {
    const { user } = params;
    const connect = this.DB.WRITE;
    await connect.users.update({
      where: {
        id: user.id,
      },
      data: {
        statistics_date_from: dayjs().toDate(),
      },
    });
    return this.SUCCESS();
  }
}

module.exports = ShippingService;
