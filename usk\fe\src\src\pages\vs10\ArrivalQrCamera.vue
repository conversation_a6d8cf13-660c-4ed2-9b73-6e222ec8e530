<template>
  <q-card
    class="tw:flex tw:flex-col tw:h-full tw:pb-[22rem] tw:tl:pb-[8rem]"
  >
    <div class="tw:text-s-design tw:text-[#333333] tw:p-4">
      カメラで、利用者カードのQRコードもしくは入出荷用のQRコードをスキャンしてください。
    </div>
    <div class="tw:flex tw:items-center tw:justify-center tw:flex-1 tw:pb-[4rem]">
      <div
        v-if="!showCanvas"
        class="tw:border-dashed tw:border-2 tw:flex tw:justify-center tw:items-center
        tw:h-[28rem] tw:w-[28rem] tw:relative"
      >
        <div>Scan QR code</div>
      </div>
      <canvas
        class="tw:h-[28rem] tw:w-[28rem] tw:relative"
        :width="canvasWidth"
        :height="canvasHeight"
        id="canvas"
        v-show="showCanvas"
      ></canvas>
      <div
        class="tw:absolute tw:h-[28rem] tw:w-[28rem] tw:flex tw:flex-center
        tw:border-[3.75rem] tw:opacity-25 tw:border-gray-50"
      ></div>
    </div>
    <!-- Footer Button -->
    <q-footer
      elevated
      class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full tw:tl:justify-start
      tw:tl:items-center tw:flex tw:justify-center tw:mt-4 tw:flex-col tw:gap-8 tw:tl:flex-row"
    >
      <div class="tw:text-[#333333] tw:text-m-design tw:font-normal">
        入荷登録方法
      </div>
      <div class="tw:flex tw:gap-4 tw:tl:gap-8">
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[20.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
          label="QRスキャナー"
          @click.prevent="handleClickQrScan"
        />
      </div>

      <BaseButton
        outline

        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
      tw:tl:w-[20.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
      tw:w-full`"
        label="伝票から手入力"
        @click.prevent="handleClickManualRegistration"
      />
    </q-footer>
  </q-card>
</template>
<script setup>
// #region import
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import jsQR from 'jsqr';
import toast from 'utilities/toast';
import arrivalService from 'services/arrival.service';
import authService from 'services/auth.service';
import useValidate from 'composables/validate';
import { debounce } from 'lodash';
import BaseButton from 'components/base/vs/BaseButton.vue';
import commonService from 'src/shared/services/common.service';
import { useAuthStore } from 'src/stores/auth-store';
import { checkQrArrivalSchema, checkQrUserSchema } from 'src/schemas/arrival/qrChecking.schema';
import { OPTION_TYPE_ENUM } from 'src/helpers/constants';
import MESSAGE from 'src/helpers/message';

// #endregion import

// #region variable
const video = document.createElement('video');
const router = useRouter();
const { signInProxyUser } = useAuthStore();
const { validateData, errors } = useValidate();

const isScan = ref(true);
const showCanvas = ref(false);
const canvas2d = ref();
const canvasElement = ref();
const canvasWidth = ref(480);
const canvasHeight = ref(480);
const flgCamera = ref(0);
const timeoutId = ref();

// #endregion variable

// #region function
const handleRegisterArrival = debounce(async qrCode => {
  const result = await arrivalService.getDetailQrArrival({ qrCode });
  if (result.code === 401) {
    openScan();
    return;
  }
  if (result.code === 0) {
    clearTimeout(timeoutId.value);
    router.push({ name: 'arrivalRegistration', params: { qrCode } });
  } else {
    openScan();
    toast.error(result.message);
  }
}, 700);

const handleLoginProxyUser = debounce(async qrCode => {
  const result = await authService.loginInsteadLink({ qrCode });
  if (result.code === 401) {
    openScan();
    return;
  }
  if (result.code === 0) {
    clearTimeout(timeoutId.value);
    router.push({ name: 'registerProxyOutboundShipment' });
  } else {
    openScan();
    toast.error(result.message);
  }
}, 700);

const tick = async () => {
  if (video.readyState === video.HAVE_ENOUGH_DATA && isScan.value) {
    showCanvas.value = true;
    canvasWidth.value = video.videoWidth;
    canvasHeight.value = video.videoHeight;
    canvasElement.value = document.querySelector('#canvas');
    canvas2d.value = canvasElement.value.getContext('2d');
    canvas2d.value.drawImage(
      video,
      0,
      0,
      canvasWidth.value,
      canvasHeight.value
    );
    const imgData = canvas2d.value.getImageData(
      0,
      0,
      canvasWidth.value,
      canvasHeight.value
    );
    const code = jsQR(imgData.data, imgData.width, imgData.height, {
      inversionAttempts: 'dontInvert',
    });
    if (code?.data) {
      timeoutId.value = setTimeout(() => {
        toast.error(MESSAGE.MSG_LIMITS_READTIMEQR_ERROR);
      }, 5000);
      // check qr code for arrival
      const validArrival = validateData(checkQrArrivalSchema, {
        qrCode: code.data,
      });

      if (validArrival) {
        video.src = '';
        const tracks = video.srcObject.getTracks();
        tracks[0].stop();
        const qrCode = code.data.slice(-16);
        await handleRegisterArrival(qrCode);
        return;
      }

      // check qr code for proxy user
      const validProxyLogin = validateData(checkQrUserSchema, {
        qrCode: code.data,
      });
      if (validProxyLogin) {
        video.src = '';
        const tracks = video.srcObject.getTracks();
        tracks[0].stop();
        const qrCode = code.data.slice(-16);
        await handleLoginProxyUser(qrCode);
        return;
      }
      if (errors.qrCode){
        toast.error(errors.qrCode);
      }
      flgCamera.value += 1;
      return;
    }
  }
  requestAnimationFrame(tick);
};

const openScan = () => {
  if (!navigator.mediaDevices || !navigator.mediaDevices.enumerateDevices) {
    toast.error('カメラを開けません。');
    return;
  }
  isScan.value = true;
  navigator.mediaDevices
    .getUserMedia({ video: { facingMode: 'environment' } })
    .then(stream => {
      video.srcObject = stream;
      video.setAttribute('playsinline', true);
      video.play();
      requestAnimationFrame(tick);
    });
};

const handleClickQrScan = () => {
  router.push({ name: 'arrivalQrScan' });
};

const handleClickManualRegistration = async () => {
  const partnerOptionsResponse = await commonService.getOptions({
    type: OPTION_TYPE_ENUM.USER_SUPPLIER,
  });
  if (partnerOptionsResponse.payload?.length === 0) {
    toast.error(MESSAGE.MSG_NA_CUSTOMER_ERR);
  } else {
    router.push({ name: 'manualRegistration' });
  }
};

// initialization
onMounted(async () => {
  signInProxyUser(null);
  openScan();
});

onBeforeUnmount(() => {
  isScan.value = false;
  setTimeout(() => {
    video.pause();
    video.src = '';
    if (video.srcObject) {
      const tracks = video.srcObject.getTracks();
      tracks[0].stop();
    }
  }, 1000);
});
// #endregion function

watch(flgCamera, () => {
  setTimeout(openScan, 2000);
});
</script>
