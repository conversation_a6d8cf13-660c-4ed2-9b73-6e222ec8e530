<template>
  <div>
    <PopupConfirmText />
    <OutboundShipmentPDF />
    <ExportShipmentPDF />
    <q-page class="tw:mb-5">
      <q-card class="tw:p-5 tw:mb-[19rem] tw:tl:mb-[8rem]">
        <div
          class="tw:flex tw:flex-col tw:tl:flex-row tw:justify-between tw:tl:items-center tw:gap-5"
        >
          <h2 class="tw:text-l-design tw:font-bold">出荷実績詳細</h2>
          <div
            class="tw:flex tw:tl:justify-between tw:justify-center tw:tl:flex-row tw:flex-col tw:gap-5"
          >
            <BaseButton
              outline
              class="tw:rounded-[40px]"
              :class="`tw:bg-white tw:text-[#004AB9] tw:text-xs-design tw:tl:font-bold
              tw:tl:h-[3.5rem] tw:min-w-[16.5rem]`"
              label="伝票出力"
            />
            <BaseButton
              outline
              class="tw:rounded-[40px]"
              :class="`tw:bg-white tw:text-[#004AB9] tw:text-xs-design tw:tl:font-bold
              tw:tl:h-[3.5rem] tw:min-w-[16.5rem]`"
              :label="changeLabel()"
              @click="handleClickExportPdf"
            />
          </div>
        </div>
        <div class="tw:text-m-design tw:mt-5">
          <div
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 入出荷用QRコード </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0] tw:min-h-[4.2rem]"
            >
              <img
                alt=""
                id="shipment-qr-code"
                :src="qrCode?.value"
                spinner-color="white"
                class="tw:w-full tw:max-w-[200px]"
              />
            </div>
          </div>
          <div
            v-if="data.shipping_type === SHIPPING_TYPE_ENUM.NORMAL && data.code"
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 漁獲/荷口番号 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0] tw:min-h-[4.2rem]"
            >
              <span>{{ maskCodeString(data.code) }}</span>
            </div>
          </div>
          <div
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 関連する漁獲/荷口番号 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
            >
              <div :class="`tw:max-h-[11.25rem] tw:overflow-y-scroll tw:min-h-[4.2rem]`">
                <p v-for="(item, index) in data?.inventory_info_codes || []" :key="index">
                  <span class="tw:hidden tw:md:inline">
                    {{ maskCodeString(item.code) }}：{{ `${item.supplier_name}` }}
                  </span>
                  <span class="tw:inline tw:md:hidden">
                    {{ maskCodeString(item.code) }}：
                    <br />
                    {{ item.supplier_name }}
                  </span>
                </p>
              </div>
            </div>
          </div>
          <div
            v-if="
                (user.role === ROLES_ENUM_OPTIONS_VALUES.CATCH_ENTERPRISE &&
                  user.staff_type === STAFF_TYPE_ENUM.STAFF) ||
                (user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
                  user.staff_type === STAFF_TYPE_ENUM.ENTERPRISE)
              "
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 出荷者 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0] tw:min-h-[4.2rem]"
            >
              <span>{{ data.starting_user_name }}</span>
            </div>
          </div>
          <div
            v-if="
              user.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
              user.staff_type === STAFF_TYPE_ENUM.STAFF
            "
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 許可番号 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5
              tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
            >
              <span>{{ data.destination_license_number }}</span>
            </div>
          </div>
          <div
            v-if="
              CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                [STAFF_TYPE_ENUM.STAFF],
                data.starting_user
              ) && CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                [STAFF_TYPE_ENUM.ENTERPRISE],
                user
              )
            "
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 備考１ </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5
              tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
            >
              <span>{{ data.destination_user_note_1 }}</span>
            </div>
          </div>
          <div
            v-if="
              CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                [STAFF_TYPE_ENUM.STAFF],
                data.starting_user
              ) && CHECK_ROLE(
                [ROLES_ENUM.NORMAL_USER],
                [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
                [STAFF_TYPE_ENUM.ENTERPRISE],
                user
              )
            "
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 備考２ </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5
              tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
            >
              <span>{{ data.destination_user_note_2 }}</span>
            </div>
          </div>
          <div
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 出荷先（届出事業者） </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5
              tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0]"
            >
              <span>{{ data.destination_enterprise_name }}</span>
            </div>
          </div>
          <div
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 出荷先 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0] tw:min-h-[4.2rem]"
            >
              <span>
                {{ data.destination_user_name }}
              </span>
            </div>
          </div>
          <div
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 出荷量 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0] tw:flex tw:flex-col tw:min-h-[4.2rem]"
            >
              <span
                v-if="
                  data.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL ||
                  data.shipping_type === SHIPPING_TYPE_ENUM.PROXY
                "
              >
                {{ FORMAT_NUMBER(data.shipping_quantity) }}尾</span
              >
              <span>{{ FORMAT_NUMBER(data.shipping_net_weight) }}g</span>
            </div>
          </div>
          <div
            v-if="data.shipping_reason_diff"
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 差異の理由 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0] tw:min-h-[4.2rem] tw:break-words tw:whitespace-break-spaces"
            >
              <span>{{ clearHTML(linkify(data.shipping_reason_diff)) }}</span>
            </div>
          </div>
          <div
            class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row
            tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#E0E0E0]"
          >
            <div class="tw:tl:w-[30%] tw:px-5 tw:flex tw:items-center">
              <span> 出荷日 </span>
            </div>
            <div
              class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1
              tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#E0E0E0] tw:min-h-[4.2rem]"
            >
              <span>{{ FORMAT_DATE(data.shipping_date) }}</span>
            </div>
          </div>
        </div>
      </q-card>

      <q-footer
        elevated
        class="tw:bg-white tw:p-4 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)]
        tw:w-full tw:tl:justify-between tw:flex tw:justify-center tw:mt-4
        tw:flex-col tw:gap-4 tw:tl:flex-row"
      >
        <BaseButton
          outline
          class="tw:rounded-[40px]"
          :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
          tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:tl:w-[23.45rem]`"
          label="出荷実績管理に戻る"
          @click="router.back()"
        />
        <div
          class="tw:flex tw:tl:justify-between tw:justify-center
          tw:tl:flex-row tw:flex-col tw:gap-4 tw:tl:gap-5"
        >
          <BaseButton
            v-if="user.license_id && data.inventory?.is_history_cancel_locked"
            outline
            class="tw:rounded-[40px]"
            :class="`tw:bg-white tw:text-[#E80F00] tw:text-m-design tw:tl:font-bold
            tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:tl:w-[18.88rem]`"
            label="取消する"
            @click="handleClickCancel"
          />
          <BaseButton

            outline
            class="tw:rounded-[40px]"
            :class="`tw:bg-white tw:text-[#004AB9] tw:text-m-design tw:tl:font-bold
            tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem] tw:tl:w-[18.88rem]`"
            label="修正する"
            @click="handleClickEdit"
          />
        </div>
      </q-footer>
    </q-page>
  </div>
</template>
<script setup>
import { useQRCode } from '@vueuse/integrations/useQRCode';
import OutboundShipmentPDF from 'components/pdf/OutboundShipmentPDF.vue';
import PopupConfirmText from 'components/PopupConfirmText.vue';
import dayjs from 'boot/dayjs';
import { FORMAT_DATE, FORMAT_DATE_TIME_CSV, FORMAT_NUMBER, maskCodeString } from 'helpers/common';
import MESSAGE from 'helpers/message';
import html2pdf from 'html2pdf.js';
import { storeToRefs } from 'pinia';
import shippingService from 'services/shipping.service';
import { makeShippingInfoXML } from 'src/boot/print';
import ExportShipmentPDF from 'src/components/pdf/ExportShipmentPDF.vue';
import {
  ENTERPRISE_TYPE_ENUM,
  REPORT_TYPE_ENUM,
  ROLES_ENUM,
  STAFF_TYPE_ENUM,
  SHIPPING_TYPE_ENUM,
  ROLES_ENUM_OPTIONS_VALUES,
} from 'src/helpers/constants';
import { useAppStore } from 'stores/app-store';
import { useAuthStore } from 'src/stores/auth-store';
import toast from 'utilities/toast';
import { nextTick, onMounted, provide, ref } from 'vue';
import { useRouter } from 'vue-router';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import { CHECK_ROLE, clearHTML, linkify } from 'src/helpers/common';

const { settingUser } = storeToRefs(useAppStore());
const { user } = storeToRefs(useAuthStore());
const router = useRouter();
const data = ref({});
const qrCode = ref();
const isPopupConformTextPopup = ref(false);
const dataExport = ref({});
const exportShipmentPDFProvideData = ref({
  rootOrigins: [],
  distributionOrigins: [],
  catchingOrigins: [],
});

// method
const handleClickEdit = async () => {
  await router.push({
    name: 'shippingEdit',
  });
};

const handleClickCancel = async () => {
  isPopupConformTextPopup.value = true;
};

const handleClickExportPdf = async () => {
  // download pdf
  await getDataExport();
};

const mappingData = (shipping = {}, lstPdf = []) => {
  const { shippingInfo } = shipping;
  lstPdf.push(shipping);
  const lst = [];
  if (!shippingInfo) {
    return;
  }
  shippingInfo.forEach(value => {
    mappingData(value, lst);
  });

  if (lst.length) {
    lstPdf.push(lst);
  }
};

const getDataExport = async () => {
  const result = await shippingService.exportShippingDetail({
    id: router.currentRoute.value.params.id,
  });
  if (!result.payload.foreign_flag) {
    const isShowQrCode = !result.payload.arrival_date;
    if (settingUser.value.report_type === REPORT_TYPE_ENUM.PDF_FILE) {
      dataExport.value = {
        shipping_date: FORMAT_DATE(result.payload.shipping_date),
        code: result.payload.code,
        starting_enterprise: CHECK_ROLE(
          [ROLES_ENUM.NORMAL_USER],
          [ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE, ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
          [STAFF_TYPE_ENUM.ENTERPRISE],
          result.payload.starting_user
        )
          ? result.payload.starting_user_name
          : `${result.payload.starting_user_name}（${result.payload.starting_enterprise_name}）`,
        destination_enterprise:
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE],
            [],
            result.payload.destination_user
          ) ||
          CHECK_ROLE(
            [ROLES_ENUM.NORMAL_USER],
            [ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE],
            [STAFF_TYPE_ENUM.ENTERPRISE],
            result.payload.destination_user
          )
            ? result.payload.destination_user_name
            : `${result.payload.destination_user_name}（${result.payload.destination_enterprise_name}）`,
        weight: settingUser.value.display_shipment_weight
          ? result.payload.shipping_net_weight
          : undefined,
        qr_code: isShowQrCode ? result.payload.qr_code : undefined,
      };
      nextTick(async () => {
        const pdfFileData = document.getElementById('shipment-pdf-file-data');
        html2pdf()
          .from(pdfFileData)
          .set({
            filename: `出荷情報_${FORMAT_DATE_TIME_CSV()}.pdf`,
            margin: [0.5, 1.25],
            image: { type: 'jpeg', quality: 0.98 },
            html2canvas: { scale: 4 },
            jsPDF: { unit: 'in', format: 'a4', orientation: 'landscape' },
          })
          .save();
        toast.access(MESSAGE.MSG_DOWNLOAD_SHIPPINGPDF_INFO);
      });
    } else {
      const dataPrint = makeShippingInfoXML(
        result.payload.destination_enterprise.enterprise_name,
        result.payload.destination_user.name,
        maskCodeString(result.payload.code?.replaceAll('-', '')),
        FORMAT_NUMBER(result.payload.shipping_net_weight),
        // map utc to local time
        dayjs(FORMAT_DATE(result.payload.shipping_date)).toDate(),
        isShowQrCode ? `${result.payload.qr_code}` : undefined,
        result.payload.starting_enterprise.enterprise_name,
        result.payload.starting_user.name,
        1
      );

      const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;

      const aTag = document.createElement('a');
      aTag.href = href;
      aTag.click();
    }
  } else {
    const lstPdf = [];
    mappingData(result.payload, lstPdf);
    lstPdf.forEach(value => {
      if (value.shippingInfo) {
        value.sum = value.shippingInfo.reduce((acc, curr) => +acc + (+Number(curr) || 0), 0);
      }
    });
    exportShipmentPDFProvideData.value = {
      distributionOrigins: [lstPdf],
    };

    nextTick(async () => {
      const pdfFileData = document.getElementById('export-shipment-pdf-file-data');
      html2pdf()
        .from(pdfFileData)
        .set({
          filename: `輸出向け取引記録_${FORMAT_DATE_TIME_CSV()}.pdf`,
          margin: [0.5, 0.3, 0.6, 0.3],
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 4 },
          jsPDF: { unit: 'in', format: 'a3', orientation: 'landscape' },
          pagebreak: { mode: ['avoid-all', 'css', 'legacy'] },
        })
        .save();
      toast.access(MESSAGE.MSG_DOWNLOAD_EXPORTPDF_INFO);
    });
  }
};

// change label
const changeLabel = () =>
  data.value.destination_enterprise?.type === ENTERPRISE_TYPE_ENUM.FOREIGN &&
  data.value.shipping_type === SHIPPING_TYPE_ENUM.NORMAL
    ? '輸出向け取引記録PDFをダウンロード'
    : '出荷情報PDFをダウンロード';

// for confirm text popup
const popupConfirmTextProvideData = {
  isPopup: isPopupConformTextPopup,
  titlePopup: '取消確認',
  caption: '出荷実績を取り消します。よろしいですか？',
  handleCloseModal: () => {
    isPopupConformTextPopup.value = false;
  },
  handleAcceptModal: async () => {
    const result = await shippingService.cancelShipping(router.currentRoute.value.params?.id);
    if (result.code === 0) {
      toast.cancel(result.payload.message);
      isPopupConformTextPopup.value = false;
      await router.push({
        name: 'shippingList',
      });
    }
  },
};
provide('popupConfirmTextProvideData', popupConfirmTextProvideData);
provide('outBoundShipmentPDFProvideData', dataExport);
provide('exportShipmentPDFProvideData', exportShipmentPDFProvideData);

onMounted(async () => {
  const result = await shippingService.getShippingDetail(router.currentRoute.value.params?.id);
  if (result.code !== 401) {
    if (result.code === 0) {
      data.value = result.payload;
      qrCode.value = useQRCode(`${data.value.qr_code}`);
    } else {
      router.back();
    }
  }
  changeLabel();
});
</script>
